import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.core.type.TypeReference;

/**
 * Docker API Client for managing Docker containers, images, networks, and volumes
 * This class provides a Java interface to interact with Docker Engine API
 */
public class DockerApiClient {
    
    private final String dockerHost;
    private final ObjectMapper objectMapper;
    
    // Default Docker daemon socket
    private static final String DEFAULT_DOCKER_HOST = "http://localhost:2375";
    
    public DockerApiClient() {
        this(DEFAULT_DOCKER_HOST);
    }
    
    public DockerApiClient(String dockerHost) {
        this.dockerHost = dockerHost;
        this.objectMapper = new ObjectMapper();
    }
    
    // ==================== Container Operations ====================
    
    /**
     * List all containers
     * @param all - true to show all containers, false for running only
     * @return List of container information
     */
    public List<Map<String, Object>> listContainers(boolean all) throws Exception {
        String endpoint = "/containers/json" + (all ? "?all=true" : "");
        String response = sendGetRequest(endpoint);
        return objectMapper.readValue(response, new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * Create a new container
     * @param imageName - Docker image name
     * @param containerName - Name for the container
     * @param config - Container configuration
     * @return Container creation response
     */
    public Map<String, Object> createContainer(String imageName, String containerName, Map<String, Object> config) throws Exception {
        String endpoint = "/containers/create";
        if (containerName != null && !containerName.isEmpty()) {
            endpoint += "?name=" + containerName;
        }
        
        // Default configuration
        if (config == null) {
            config = Map.of("Image", imageName);
        } else if (!config.containsKey("Image")) {
            config.put("Image", imageName);
        }
        
        String jsonPayload = objectMapper.writeValueAsString(config);
        String response = sendPostRequest(endpoint, jsonPayload);
        return objectMapper.readValue(response, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Start a container
     * @param containerId - Container ID or name
     * @return true if successful
     */
    public boolean startContainer(String containerId) throws Exception {
        String endpoint = "/containers/" + containerId + "/start";
        int responseCode = sendPostRequestForStatusCode(endpoint, "");
        return responseCode == 204; // Docker returns 204 for successful start
    }
    
    /**
     * Stop a container
     * @param containerId - Container ID or name
     * @param timeout - Timeout in seconds
     * @return true if successful
     */
    public boolean stopContainer(String containerId, int timeout) throws Exception {
        String endpoint = "/containers/" + containerId + "/stop?t=" + timeout;
        int responseCode = sendPostRequestForStatusCode(endpoint, "");
        return responseCode == 204; // Docker returns 204 for successful stop
    }
    
    /**
     * Remove a container
     * @param containerId - Container ID or name
     * @param force - Force removal of running container
     * @return true if successful
     */
    public boolean removeContainer(String containerId, boolean force) throws Exception {
        String endpoint = "/containers/" + containerId + (force ? "?force=true" : "");
        int responseCode = sendDeleteRequestForStatusCode(endpoint);
        return responseCode == 204;
    }
    
    /**
     * Get container logs
     * @param containerId - Container ID or name
     * @param follow - Follow log output
     * @param stdout - Include stdout
     * @param stderr - Include stderr
     * @return Container logs
     */
    public String getContainerLogs(String containerId, boolean follow, boolean stdout, boolean stderr) throws Exception {
        String endpoint = String.format("/containers/%s/logs?follow=%s&stdout=%s&stderr=%s", 
                                      containerId, follow, stdout, stderr);
        return sendGetRequest(endpoint);
    }
    
    // ==================== Image Operations ====================
    
    /**
     * List all images
     * @return List of image information
     */
    public List<Map<String, Object>> listImages() throws Exception {
        String endpoint = "/images/json";
        String response = sendGetRequest(endpoint);
        return objectMapper.readValue(response, new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * Pull an image from registry
     * @param imageName - Image name with optional tag
     * @return Pull operation response
     */
    public String pullImage(String imageName) throws Exception {
        String endpoint = "/images/create?fromImage=" + imageName;
        return sendPostRequest(endpoint, "");
    }
    
    /**
     * Remove an image
     * @param imageId - Image ID or name
     * @param force - Force removal
     * @return true if successful
     */
    public boolean removeImage(String imageId, boolean force) throws Exception {
        String endpoint = "/images/" + imageId + (force ? "?force=true" : "");
        int responseCode = sendDeleteRequestForStatusCode(endpoint);
        return responseCode == 200;
    }
    
    // ==================== Network Operations ====================
    
    /**
     * List all networks
     * @return List of network information
     */
    public List<Map<String, Object>> listNetworks() throws Exception {
        String endpoint = "/networks";
        String response = sendGetRequest(endpoint);
        return objectMapper.readValue(response, new TypeReference<List<Map<String, Object>>>() {});
    }
    
    /**
     * Create a network
     * @param networkName - Network name
     * @param driver - Network driver (bridge, overlay, etc.)
     * @return Network creation response
     */
    public Map<String, Object> createNetwork(String networkName, String driver) throws Exception {
        String endpoint = "/networks/create";
        Map<String, Object> config = Map.of(
            "Name", networkName,
            "Driver", driver != null ? driver : "bridge"
        );
        
        String jsonPayload = objectMapper.writeValueAsString(config);
        String response = sendPostRequest(endpoint, jsonPayload);
        return objectMapper.readValue(response, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Remove a network
     * @param networkId - Network ID or name
     * @return true if successful
     */
    public boolean removeNetwork(String networkId) throws Exception {
        String endpoint = "/networks/" + networkId;
        int responseCode = sendDeleteRequestForStatusCode(endpoint);
        return responseCode == 204;
    }
    
    // ==================== Volume Operations ====================
    
    /**
     * List all volumes
     * @return List of volume information
     */
    public Map<String, Object> listVolumes() throws Exception {
        String endpoint = "/volumes";
        String response = sendGetRequest(endpoint);
        return objectMapper.readValue(response, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Create a volume
     * @param volumeName - Volume name
     * @return Volume creation response
     */
    public Map<String, Object> createVolume(String volumeName) throws Exception {
        String endpoint = "/volumes/create";
        Map<String, Object> config = Map.of("Name", volumeName);
        
        String jsonPayload = objectMapper.writeValueAsString(config);
        String response = sendPostRequest(endpoint, jsonPayload);
        return objectMapper.readValue(response, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Remove a volume
     * @param volumeName - Volume name
     * @return true if successful
     */
    public boolean removeVolume(String volumeName) throws Exception {
        String endpoint = "/volumes/" + volumeName;
        int responseCode = sendDeleteRequestForStatusCode(endpoint);
        return responseCode == 204;
    }
    
    // ==================== System Operations ====================
    
    /**
     * Get Docker system information
     * @return System information
     */
    public Map<String, Object> getSystemInfo() throws Exception {
        String endpoint = "/info";
        String response = sendGetRequest(endpoint);
        return objectMapper.readValue(response, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Get Docker version information
     * @return Version information
     */
    public Map<String, Object> getVersion() throws Exception {
        String endpoint = "/version";
        String response = sendGetRequest(endpoint);
        return objectMapper.readValue(response, new TypeReference<Map<String, Object>>() {});
    }
    
    /**
     * Ping Docker daemon
     * @return true if daemon is accessible
     */
    public boolean ping() throws Exception {
        try {
            String endpoint = "/_ping";
            sendGetRequest(endpoint);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    // ==================== HTTP Request Helper Methods ====================

    /**
     * Send GET request to Docker API
     */
    private String sendGetRequest(String endpoint) throws Exception {
        URL url = new URL(dockerHost + endpoint);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Content-Type", "application/json");

        int responseCode = connection.getResponseCode();
        if (responseCode >= 200 && responseCode < 300) {
            return readResponse(connection.getInputStream());
        } else {
            String errorResponse = readResponse(connection.getErrorStream());
            throw new RuntimeException("HTTP " + responseCode + ": " + errorResponse);
        }
    }

    /**
     * Send POST request to Docker API
     */
    private String sendPostRequest(String endpoint, String jsonPayload) throws Exception {
        URL url = new URL(dockerHost + endpoint);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        if (jsonPayload != null && !jsonPayload.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
        }

        int responseCode = connection.getResponseCode();
        if (responseCode >= 200 && responseCode < 300) {
            return readResponse(connection.getInputStream());
        } else {
            String errorResponse = readResponse(connection.getErrorStream());
            throw new RuntimeException("HTTP " + responseCode + ": " + errorResponse);
        }
    }

    /**
     * Send POST request and return status code only
     */
    private int sendPostRequestForStatusCode(String endpoint, String jsonPayload) throws Exception {
        URL url = new URL(dockerHost + endpoint);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("POST");
        connection.setRequestProperty("Content-Type", "application/json");
        connection.setDoOutput(true);

        if (jsonPayload != null && !jsonPayload.isEmpty()) {
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = jsonPayload.getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
            }
        }

        return connection.getResponseCode();
    }

    /**
     * Send DELETE request and return status code only
     */
    private int sendDeleteRequestForStatusCode(String endpoint) throws Exception {
        URL url = new URL(dockerHost + endpoint);
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("DELETE");
        connection.setRequestProperty("Content-Type", "application/json");

        return connection.getResponseCode();
    }

    /**
     * Read response from input stream
     */
    private String readResponse(InputStream inputStream) throws Exception {
        if (inputStream == null) {
            return "";
        }

        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line).append("\n");
            }
        }
        return response.toString();
    }
}
